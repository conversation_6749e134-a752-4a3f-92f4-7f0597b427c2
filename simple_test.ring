# ==============================================================================
# Simple Test for Morgen AI Generator
# اختبار بسيط لمولد مرجان الذكي
# ==============================================================================

load "morgen/intelligent_interface.ring"

func main {
    see "🧪 اختبار بسيط لمولد مرجان الذكي" + nl
    see copy("-", 35) + nl
    
    try {
        # اختبار 1: إنشاء الواجهة الذكية
        see "1️⃣ اختبار إنشاء الواجهة الذكية..." + nl
        oInterface = new IntelligentInterface()
        see "✅ تم إنشاء الواجهة بنجاح!" + nl + nl
        
        # اختبار 2: توليد نص بسيط
        see "2️⃣ اختبار توليد النص..." + nl
        cText = oInterface.oTextGenerator.generateText("الذكاء الاصطناعي", 10, "narrative")
        see "النص المولد: " + cText + nl
        see "✅ تم توليد النص بنجاح!" + nl + nl
        
        # اختبار 3: توليد كود بسيط
        see "3️⃣ اختبار توليد الكود..." + nl
        cCode = oInterface.oCodeGenerator.generateCode("دالة بسيطة", "function", [])
        see "الكود المولد:" + nl + cCode + nl
        see "✅ تم توليد الكود بنجاح!" + nl + nl
        
        # اختبار 4: تخزين واسترجاع المعرفة
        see "4️⃣ اختبار نظام المعرفة..." + nl
        oInterface.oKnowledgeStorage.storeKnowledge("test", "اختبار المعرفة", 0.8)
        aResults = oInterface.oKnowledgeStorage.retrieveKnowledge("اختبار", 1)
        see "عدد النتائج المسترجعة: " + len(aResults) + nl
        see "✅ تم اختبار نظام المعرفة بنجاح!" + nl + nl
        
        # اختبار 5: اكتشاف نوع الطلب
        see "5️⃣ اختبار اكتشاف نوع الطلب..." + nl
        cType1 = oInterface.detectRequestType("اكتب لي قصة")
        cType2 = oInterface.detectRequestType("أنشئ دالة")
        see "نوع الطلب الأول: " + cType1 + nl
        see "نوع الطلب الثاني: " + cType2 + nl
        see "✅ تم اختبار اكتشاف الطلبات بنجاح!" + nl + nl
        
        # عرض الإحصائيات
        see "📊 إحصائيات النظام:" + nl
        oStatus = oInterface.getSystemStatus()
        see "عدد الرموز: " + oStatus["symbols_count"] + nl
        see "حجم المعرفة: " + oStatus["knowledge_count"] + nl
        see "طول المحادثة: " + oStatus["conversation_length"] + nl
        
        see nl + "🎉 جميع الاختبارات نجحت! النظام يعمل بشكل صحيح." + nl
        
    catch
        see "❌ خطأ في الاختبار: " + cCatchError + nl
        see "يرجى التحقق من الملفات والمتطلبات." + nl
    }
}
