# ==============================================================================
# Quick Start for Morgen AI Generator
# بداية سريعة لمولد مرجان الذكي
# ==============================================================================

load "morgen/intelligent_interface.ring"

func main {
    # عرض الشعار
    displayLogo()
    
    # عرض القائمة
    displayMenu()
    
    # قراءة اختيار المستخدم
    see "اختر من القائمة: "
    give cChoice
    
    # معالجة الاختيار
    processChoice(cChoice)
}

func displayLogo {
    see nl
    see "🌟 ═══════════════════════════════════════════════════════════ 🌟" + nl
    see "                    مولد مرجان الذكي                        " + nl
    see "                  Morgen AI Generator                       " + nl
    see "🌟 ═══════════════════════════════════════════════════════════ 🌟" + nl
    see nl
    see "🎯 نظام متطور لتوليد النصوص والأكواد بالذكاء الاصطناعي" + nl
    see "✨ يستخدم الرياضيات المرجانية ودورة الأحلام الثلاثية" + nl
    see nl
}

func displayMenu {
    see "📋 اختر ما تريد تجربته:" + nl
    see "═══════════════════════════" + nl
    see "1️⃣  عرض توضيحي شامل" + nl
    see "2️⃣  اختبار سريع للنظام" + nl
    see "3️⃣  توليد نص عربي" + nl
    see "4️⃣  توليد كود برمجي" + nl
    see "5️⃣  وضع تفاعلي كامل" + nl
    see "6️⃣  معلومات النظام" + nl
    see "0️⃣  خروج" + nl
    see "═══════════════════════════" + nl
}

func processChoice cChoice {
    cChoice = trim(cChoice)
    
    if cChoice = "1" {
        runFullDemo()
    elseif cChoice = "2"
        runQuickTest()
    elseif cChoice = "3"
        generateSampleText()
    elseif cChoice = "4"
        generateSampleCode()
    elseif cChoice = "5"
        runInteractiveMode()
    elseif cChoice = "6"
        showSystemInfo()
    elseif cChoice = "0"
        see "👋 شكراً لاستخدام مولد مرجان الذكي!" + nl
        return
    else
        see "❌ اختيار غير صحيح. يرجى المحاولة مرة أخرى." + nl
        main()
    }
}

func runFullDemo {
    see "🎬 تشغيل العرض التوضيحي الشامل..." + nl
    system("ring demo_morgen_generator.ring")
}

func runQuickTest {
    see "🧪 تشغيل الاختبار السريع..." + nl
    system("ring simple_test.ring")
}

func generateSampleText {
    see "📝 توليد نص عربي تجريبي..." + nl + nl
    
    try {
        oInterface = new IntelligentInterface()
        
        see "أدخل موضوع النص (أو اضغط Enter للموضوع الافتراضي): "
        give cTopic
        
        if trim(cTopic) = "" {
            cTopic = "الذكاء الاصطناعي والمستقبل"
        }
        
        see "🎨 جاري توليد النص..." + nl
        cText = oInterface.oTextGenerator.generateText(cTopic, 40, "narrative")
        
        see nl + "✨ النص المولد:" + nl
        see "═══════════════════════════════════════════════════════════" + nl
        see cText + nl
        see "═══════════════════════════════════════════════════════════" + nl
        
        see nl + "💾 هل تريد حفظ هذا النص؟ (نعم/لا): "
        give cSave
        if lower(trim(cSave)) = "نعم" {
            oInterface.oKnowledgeStorage.storeKnowledge("generated_text", cText, 0.8)
            see "✅ تم حفظ النص في نظام المعرفة." + nl
        }
        
    catch
        see "❌ خطأ في توليد النص: " + cCatchError + nl
    }
    
    see nl + "اضغط Enter للعودة للقائمة الرئيسية..."
    give cDummy
    main()
}

func generateSampleCode {
    see "💻 توليد كود برمجي تجريبي..." + nl + nl
    
    try {
        oInterface = new IntelligentInterface()
        
        see "أدخل وصف الكود المطلوب (أو اضغط Enter للوصف الافتراضي): "
        give cDescription
        
        if trim(cDescription) = "" {
            cDescription = "دالة لحساب مجموع الأرقام"
        }
        
        see "اختر نوع الكود:" + nl
        see "1. دالة (function)" + nl
        see "2. كلاس (class)" + nl
        see "3. خوارزمية (algorithm)" + nl
        see "اختيارك (افتراضي: 1): "
        give cTypeChoice
        
        cCodeType = "function"
        if cTypeChoice = "2" {
            cCodeType = "class"
        elseif cTypeChoice = "3"
            cCodeType = "algorithm"
        }
        
        see "⚡ جاري توليد الكود..." + nl
        cCode = oInterface.oCodeGenerator.generateCode(cDescription, cCodeType, [])
        
        see nl + "✨ الكود المولد:" + nl
        see "═══════════════════════════════════════════════════════════" + nl
        see cCode + nl
        see "═══════════════════════════════════════════════════════════" + nl
        
        see nl + "💾 هل تريد حفظ هذا الكود؟ (نعم/لا): "
        give cSave
        if lower(trim(cSave)) = "نعم" {
            oInterface.oKnowledgeStorage.storeKnowledge("generated_code", cCode, 0.9)
            see "✅ تم حفظ الكود في نظام المعرفة." + nl
        }
        
    catch
        see "❌ خطأ في توليد الكود: " + cCatchError + nl
    }
    
    see nl + "اضغط Enter للعودة للقائمة الرئيسية..."
    give cDummy
    main()
}

func runInteractiveMode {
    see "🎮 تشغيل الوضع التفاعلي الكامل..." + nl
    system("ring morgen_ai_generator.ring")
}

func showSystemInfo {
    see "ℹ️ معلومات نظام مولد مرجان الذكي" + nl
    see "═══════════════════════════════════════════════════════════" + nl
    see "🏷️ الاسم: مولد مرجان الذكي (Morgen AI Generator)" + nl
    see "📦 الإصدار: 1.0.0" + nl
    see "🔧 لغة البرمجة: Ring Language" + nl
    see "🎯 الغرض: توليد النصوص العربية والأكواد البرمجية" + nl
    see nl
    see "✨ الميزات الرئيسية:" + nl
    see "   • توليد نصوص عربية بأنماط متعددة (سردي، وصفي، حجاجي)" + nl
    see "   • توليد أكواد برمجية بلغات مختلفة (Ring, Python, JavaScript)" + nl
    see "   • نظام ذاكرة تكيفي يتعلم من التفاعلات" + nl
    see "   • واجهة تفاعلية ذكية تفهم الطلبات الطبيعية" + nl
    see "   • استخدام الرياضيات المرجانية ودورة الأحلام الثلاثية" + nl
    see nl
    see "🧠 التقنيات المستخدمة:" + nl
    see "   • الفضاء المرجاني القطبي للمعرفة" + nl
    see "   • دورة الأحلام: حل + لم + حلم" + nl
    see "   • نظام الرنين المرجاني للبحث" + nl
    see "   • العناقيد الدلالية التلقائية" + nl
    see "   • الفهرسة القطبية المتقدمة" + nl
    see nl
    see "📁 الملفات الرئيسية:" + nl
    see "   • morgen_ai_generator.ring - التطبيق الرئيسي" + nl
    see "   • demo_morgen_generator.ring - العرض التوضيحي" + nl
    see "   • simple_test.ring - الاختبار البسيط" + nl
    see "   • quick_start_generator.ring - هذا الملف" + nl
    see nl
    see "🔗 المكونات الأساسية:" + nl
    see "   • intelligent_interface.ring - الواجهة الذكية" + nl
    see "   • advanced_text_generator.ring - مولد النصوص" + nl
    see "   • advanced_code_generator.ring - مولد الأكواد" + nl
    see "   • advanced_knowledge_storage.ring - نظام المعرفة" + nl
    see "═══════════════════════════════════════════════════════════" + nl
    
    try {
        see nl + "🔍 فحص حالة النظام..." + nl
        oInterface = new IntelligentInterface()
        oStatus = oInterface.getSystemStatus()
        
        see "✅ النظام يعمل بشكل صحيح!" + nl
        see "📊 الإحصائيات الحالية:" + nl
        see "   • عدد الرموز: " + oStatus["symbols_count"] + nl
        see "   • حجم المعرفة: " + oStatus["knowledge_count"] + nl
        see "   • العصر الحالي: " + oStatus["current_epoch"] + nl
        
    catch
        see "⚠️ تحذير: قد تكون هناك مشكلة في بعض المكونات." + nl
        see "الخطأ: " + cCatchError + nl
    }
    
    see nl + "اضغط Enter للعودة للقائمة الرئيسية..."
    give cDummy
    main()
}

