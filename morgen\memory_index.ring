# ==============================================================================
# File: memory_index.ring
# Description: Fast spatial/resonance/genome indexing for MorgenSpace symbols
# Author: Morgen AI Project
# ==============================================================================

load "geometry_utils.ring"

# ==============================================================================
# Class: MorgenKnowledgeIndex
# Purpose: Provide fast store/retrieve over the inherent Morgen math structure
#          using polar bins (radius/angle), resonance buckets, and genome signature
# ==============================================================================
class MorgenKnowledgeIndex {
    # --- Public Attributes ---
    oMorgenSpace          # Reference to space
    nRadBins              # Number of radial bins
    nAngleBins            # Number of angular bins (0-360)
    nResBins              # Number of resonance buckets

    aSpatialIndex         # List of buckets: ["key"=cKey, "ids"=[...]]
    aResonanceIndex       # List of buckets: ["key"=cKey, "ids"=[...]]
    aTypeIndex            # List of buckets: ["key"=cKey, "ids"=[...]]
    aGenomeSigIndex       # List of buckets: ["key"=cKey, "ids"=[...]]

    func init oSpace, nRadBins_, nAngleBins_, nResBins_ {
        if not isObject(oSpace) { raise("MorgenKnowledgeIndex.init: Invalid space object") }
        self.oMorgenSpace = oSpace
        self.nRadBins = max(4, nRadBins_)
        self.nAngleBins = max(8, nAngleBins_)
        self.nResBins = max(4, nResBins_)
        self.aSpatialIndex = []
        self.aResonanceIndex = []
        self.aTypeIndex = []
        self.aGenomeSigIndex = []
        return self
    }

    # Build full index from space
    func buildIndex {
        self.aSpatialIndex = []
        self.aResonanceIndex = []
        self.aTypeIndex = []
        self.aGenomeSigIndex = []
        for oSymbol in self.oMorgenSpace.aSymbols {
            if isObject(oSymbol) { self.indexSymbol(oSymbol) }
        }
        see "Knowledge index built over " + len(self.oMorgenSpace.aSymbols) + " symbols" + nl
    }

    # Index a single symbol (call when symbol is added)
    func indexSymbol oSymbol {
        if not isObject(oSymbol) { return }
        cSKey = self.computeSpatialKey(oSymbol.nRadius, oSymbol.nAngle)
        self.addToIndex(self.aSpatialIndex, cSKey, oSymbol.cId)

        cRKey = self.computeResKey(oSymbol.nResonance)
        self.addToIndex(self.aResonanceIndex, cRKey, oSymbol.cId)

        cTKey = "type:" + oSymbol.cType
        self.addToIndex(self.aTypeIndex, cTKey, oSymbol.cId)

        cGKey = self.computeGenomeSignature(oSymbol.aGenome)
        self.addToIndex(self.aGenomeSigIndex, cGKey, oSymbol.cId)
    }

    # ------------------------- Retrieval APIs -------------------------
    # Query by spatial region (radius/angle tolerance)
    func queryRegion nRadius, nAngle, nTolerance, nLimit {
        aCandidateIds = []
        nRBin = self.quantizeRadius(nRadius)
        nABin = self.quantizeAngle(nAngle)
        nTolBins = max(0, floor(nTolerance * self.nRadBins))
        nAngTolBins = max(0, floor((nTolerance * 360) / (360 / self.nAngleBins)))

        for dr = -nTolBins to nTolBins {
            nRB = nRBin + dr
            if nRB < 0 or nRB >= self.nRadBins { continue }
            for da = -nAngTolBins to nAngTolBins {
                nAB = (nABin + da)
                # wrap angle bins
                while nAB < 0 { nAB += self.nAngleBins }
                while nAB >= self.nAngleBins { nAB -= self.nAngleBins }
                cKey = "r:" + nRB + ",a:" + nAB
                aIds = self.getIds(self.aSpatialIndex, cKey)
                for cId in aIds { add(aCandidateIds, cId) }
            }
        }
        aCandidateIds = self.uniqueIds(aCandidateIds)

        # Score candidates by distance/resonance and return top nLimit symbols
        aScored = []
        for cId in aCandidateIds {
            oSym = self.oMorgenSpace.getSymbolById(cId)
            if isObject(oSym) {
                nDist = calculatePolarDistance(nRadius, nAngle, oSym.nRadius, oSym.nAngle)
                nScore = (1 / (1 + nDist)) * (1 + (oSym.nResonance / 10))
                add(aScored, [oSym, nScore])
            }
        }
        aScored = self.sortByScoreDesc(aScored)
        return self.takeTopSymbols(aScored, max(1, nLimit))
    }

    # Query by resonance range
    func queryResonance nMin, nMax, nLimit {
        aCandidateIds = []
        nMinB = self.quantizeRes(nMin)
        nMaxB = self.quantizeRes(nMax)
        if nMinB > nMaxB { temp = nMinB nMinB = nMaxB nMaxB = temp }
        for b = nMinB to nMaxB {
            cKey = "res:" + b
            aIds = self.getIds(self.aResonanceIndex, cKey)
            for cId in aIds { add(aCandidateIds, cId) }
        }
        aCandidateIds = self.uniqueIds(aCandidateIds)
        aScored = []
        for cId in aCandidateIds {
            oSym = self.oMorgenSpace.getSymbolById(cId)
            if isObject(oSym) {
                # Prefer mid-range between nMin/nMax
                nCenter = (nMin + nMax) / 2
                nScore = 1 - (fabs(oSym.nResonance - nCenter) / max(0.0001, (nMax - nMin)))
                if nScore < 0 { nScore = 0 }
                add(aScored, [oSym, nScore])
            }
        }
        aScored = self.sortByScoreDesc(aScored)
        return self.takeTopSymbols(aScored, max(1, nLimit))
    }

    # Query by genome similarity with signature prefilter
    func queryGenome aGenome, nThreshold, nLimit {
        aCandidateIds = []
        cSig = self.computeGenomeSignature(aGenome)
        # Gather same bucket and neighboring signature variants (length ±1 and ones ±1)
        aNeighborKeys = self.genomeNeighborKeys(cSig)
        for cKey in aNeighborKeys {
            aIds = self.getIds(self.aGenomeSigIndex, cKey)
            for cId in aIds { add(aCandidateIds, cId) }
        }
        aCandidateIds = self.uniqueIds(aCandidateIds)

        aScored = []
        for cId in aCandidateIds {
            oSym = self.oMorgenSpace.getSymbolById(cId)
            if isObject(oSym) {
                nSim = calculateGenomeSimilarity(aGenome, oSym.aGenome)
                if nSim >= nThreshold {
                    add(aScored, [oSym, nSim])
                }
            }
        }
        aScored = self.sortByScoreDesc(aScored)
        return self.takeTopSymbols(aScored, max(1, nLimit))
    }

    # ------------------------- Helpers -------------------------
    func computeSpatialKey nRadius, nAngle {
        nRB = self.quantizeRadius(nRadius)
        nAB = self.quantizeAngle(nAngle)
        return "r:" + nRB + ",a:" + nAB
    }

    func quantizeRadius nRadius {
        if nRadius < 0 { nRadius = 0 } if nRadius > 1 { nRadius = 1 }
        nBin = floor(nRadius * self.nRadBins)
        if nBin >= self.nRadBins { nBin = self.nRadBins - 1 }
        return nBin
    }

    func quantizeAngle nAngle {
        while nAngle < 0 { nAngle += 360 }
        while nAngle >= 360 { nAngle -= 360 }
        nBinSize = 360 / self.nAngleBins
        nBin = floor(nAngle / nBinSize)
        if nBin >= self.nAngleBins { nBin = self.nAngleBins - 1 }
        return nBin
    }

    func computeResKey nResonance {
        nB = self.quantizeRes(nResonance)
        return "res:" + nB
    }

    func quantizeRes nResonance {
        # Assume resonance roughly 0..10; clamp
        if nResonance < 0 { nResonance = 0 }
        if nResonance > 10 { nResonance = 10 }
        nBin = floor((nResonance / 10.0) * self.nResBins)
        if nBin >= self.nResBins { nBin = self.nResBins - 1 }
        return nBin
    }

    func computeGenomeSignature aGenome {
        nLen = len(aGenome)
        nOnes = 0
        for g in aGenome { if g = 1 { nOnes++ } }
        return "sig:L" + nLen + ":O" + nOnes
    }

    func genomeNeighborKeys cSig {
        # cSig format: "sig:L<n>:O<o>"
        nPosL = find(cSig, "L")
        nPosO = find(cSig, ":O")
        cL = substr(cSig, nPosL+1, nPosO - (nPosL+1))
        cO = substr(cSig, nPosO+2, len(cSig) - (nPosO+1))
        nL = number(cL)
        nO = number(cO)
        aKeys = []
        for dL = -1 to 1 {
            for dO = -1 to 1 {
                cKey = "sig:L" + (nL + dL) + ":O" + (nO + dO)
                add(aKeys, cKey)
            }
        }
        # Always include original
        add(aKeys, cSig)
        return aKeys
    }

    func addToIndex aIndex, cKey, cId {
        # Find bucket
        for oBucket in aIndex {
            if oBucket["key"] = cKey {
                aIds = oBucket["ids"]
                # ensure not duplicate
                bFound = false
                for x in aIds { if x = cId { bFound = true break } }
                if not bFound { add(aIds, cId) }
                oBucket["ids"] = aIds
                return
            }
        }
        add(aIndex, ["key"=cKey, "ids"=[cId]])
    }

    func getIds aIndex, cKey {
        for oBucket in aIndex {
            if oBucket["key"] = cKey { return oBucket["ids"] }
        }
        return []
    }

    func uniqueIds aIds {
        aOut = []
        for cId in aIds {
            bHave = false
            for c in aOut { if c = cId { bHave = true break } }
            if not bHave { add(aOut, cId) }
        }
        return aOut
    }

    func sortByScoreDesc aScored {
        # Bubble sort by score at index 2 (aScored item = [oSym, nScore])
        for i = 1 to len(aScored) - 1 {
            for j = 1 to len(aScored) - i {
                if aScored[j][2] < aScored[j+1][2] {
                    temp = aScored[j]
                    aScored[j] = aScored[j+1]
                    aScored[j+1] = temp
                }
            }
        }
        return aScored
    }

    func takeTopSymbols aScored, nLimit {
        aOut = []
        nMax = min(nLimit, len(aScored))
        for i = 1 to nMax { add(aOut, aScored[i][1]) }
        return aOut
    }
}

